كيفية نقل البيانات من مجلد p إلى قاعدة البيانات الحالية
=======================================================

الهدف:
-------
نقل جميع البيانات من قاعدة البيانات الموجودة في مجلد "p" (التي جاءت مع المشروع من زميلك) 
إلى قاعدة البيانات على جهازك الحالي.

الخطوات:
---------

1. تشغيل التطبيق:
   - افتح التطبيق
   - سجل دخولك بحساب المدير أو الموظف

2. فتح أداة نقل البيانات:
   - ستجد زر أزرق اللون بعنوان "نقل البيانات" في الشاشة الرئيسية
   - اضغط على هذا الزر

3. تأكيد النقل:
   - ستظهر رسالة تأكيد تسأل إذا كنت متأكد من نقل البيانات
   - اضغط "نعم" للمتابعة

4. انتظار النقل:
   - ستبدأ عملية النقل تلقائياً
   - ستظهر رسائل تقدم العمل
   - انتظر حتى انتهاء العملية

5. النتيجة:
   - ستظهر رسالة تخبرك بعدد الجداول التي تم نقلها بنجاح
   - اضغط "موافق" لإغلاق الرسالة

ما يحدث أثناء النقل:
-------------------
- يتم حذف جميع البيانات الحالية في قاعدة البيانات على جهازك
- يتم نسخ جميع البيانات من مجلد p إلى قاعدة البيانات الحالية
- يتم نقل الجداول التالية:
  * المستخدمين
  * المشتركين
  * أفراد العائلة
  * المتبرعين
  * المواد
  * التبرعات
  * الاحتياجات

ملاحظات مهمة:
--------------
⚠️ تحذير: سيتم حذف جميع البيانات الحالية واستبدالها بالبيانات من مجلد p

✅ تأكد من وجود ملف Project_DB.mdf في مجلد p
✅ تأكد من أن SQL Server يعمل على جهازك
✅ لا تغلق التطبيق أثناء عملية النقل
✅ انتظر حتى ظهور رسالة "تم النقل بنجاح"

في حالة المشاكل:
-----------------
إذا ظهرت رسالة خطأ، تأكد من:
- وجود ملف Project_DB.mdf في مجلد p
- أن SQL Server Express يعمل على جهازك
- أن لديك صلاحيات الكتابة في قاعدة البيانات

للمساعدة:
----------
إذا واجهت أي مشاكل، احتفظ بنص رسالة الخطأ وتواصل مع المطور.

مدة العملية:
------------
عادة تستغرق العملية من 30 ثانية إلى دقيقتين حسب حجم البيانات.
