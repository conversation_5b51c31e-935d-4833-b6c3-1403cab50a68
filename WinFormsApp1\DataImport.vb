﻿Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports System.Data
Imports System.IO

Public Class DataImport
    ' اتصال قاعدة البيانات الحالية
    Private currentConn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=Project_DB;Integrated Security=True")
    
    ' متغير لحفظ البيانات المستوردة
    Private importedData As New Dictionary(Of String, DataTable)

    Private Sub DataImport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة النموذج
        Label_Status.Text = "جاهز للاستيراد..."
        ProgressBar1.Value = 0
        Button_Import.Enabled = False
        Button_Preview.Enabled = False
    End Sub

    Private Sub Button_SelectFile_Click(sender As Object, e As EventArgs) Handles Button_SelectFile.Click
        ' اختيار ملف قاعدة البيانات
        If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
            TextBox_FilePath.Text = OpenFileDialog1.FileName
            Button_Preview.Enabled = True
            
            ' تحديد نوع الملف تلقائياً
            Dim extension As String = Path.GetExtension(OpenFileDialog1.FileName).ToLower()
            Select Case extension
                Case ".mdb", ".accdb"
                    RadioButton_Access.Checked = True
                Case ".mdf"
                    RadioButton_SqlServer.Checked = True
                Case ".xlsx", ".xls"
                    RadioButton_Excel.Checked = True
            End Select
            
            Label_Status.Text = "تم اختيار الملف: " & Path.GetFileName(OpenFileDialog1.FileName)
        End If
    End Sub

    Private Sub Button_Preview_Click(sender As Object, e As EventArgs) Handles Button_Preview.Click
        ' معاينة البيانات قبل الاستيراد
        Try
            Label_Status.Text = "جاري تحميل البيانات للمعاينة..."
            ProgressBar1.Style = ProgressBarStyle.Marquee
            
            ' تحميل البيانات حسب نوع الملف
            If RadioButton_Access.Checked Then
                LoadAccessData()
            ElseIf RadioButton_SqlServer.Checked Then
                LoadSqlServerData()
            ElseIf RadioButton_Excel.Checked Then
                LoadExcelData()
            End If
            
            ' عرض أول جدول في المعاينة
            If importedData.Count > 0 Then
                Dim firstTable = importedData.First()
                DataGridView_Preview.DataSource = firstTable.Value
                Label_Status.Text = $"تم تحميل {importedData.Count} جدول. معاينة: {firstTable.Key} ({firstTable.Value.Rows.Count} سجل)"
                Button_Import.Enabled = True
            Else
                Label_Status.Text = "لم يتم العثور على بيانات في الملف المحدد"
            End If
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Label_Status.Text = "فشل في تحميل البيانات"
        Finally
            ProgressBar1.Style = ProgressBarStyle.Continuous
            ProgressBar1.Value = 0
        End Try
    End Sub

    Private Sub LoadAccessData()
        ' تحميل البيانات من ملف Access
        Dim connectionString As String = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={TextBox_FilePath.Text};"
        
        Using conn As New OleDbConnection(connectionString)
            conn.Open()
            
            ' الحصول على أسماء الجداول
            Dim tables As DataTable = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)
            
            For Each row As DataRow In tables.Rows
                Dim tableName As String = row("TABLE_NAME").ToString()
                
                ' تخطي جداول النظام
                If Not tableName.StartsWith("MSys") AndAlso row("TABLE_TYPE").ToString() = "TABLE" Then
                    ' تحميل بيانات الجدول
                    Dim query As String = $"SELECT * FROM [{tableName}]"
                    Using adapter As New OleDbDataAdapter(query, conn)
                        Dim dt As New DataTable()
                        adapter.Fill(dt)
                        importedData(tableName) = dt
                    End Using
                End If
            Next
        End Using
    End Sub

    Private Sub LoadSqlServerData()
        ' تحميل البيانات من ملف SQL Server
        Dim connectionString As String = $"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename={TextBox_FilePath.Text};Integrated Security=True"
        
        Using conn As New SqlConnection(connectionString)
            conn.Open()
            
            ' الحصول على أسماء الجداول
            Dim query As String = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
            Using cmd As New SqlCommand(query, conn)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    Dim tableNames As New List(Of String)
                    While reader.Read()
                        tableNames.Add(reader("TABLE_NAME").ToString())
                    End While
                    reader.Close()
                    
                    ' تحميل بيانات كل جدول
                    For Each tableName As String In tableNames
                        Dim tableQuery As String = $"SELECT * FROM [{tableName}]"
                        Using adapter As New SqlDataAdapter(tableQuery, conn)
                            Dim dt As New DataTable()
                            adapter.Fill(dt)
                            importedData(tableName) = dt
                        End Using
                    Next
                End Using
            End Using
        End Using
    End Sub

    Private Sub LoadExcelData()
        ' تحميل البيانات من ملف Excel
        Dim connectionString As String = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={TextBox_FilePath.Text};Extended Properties='Excel 12.0 Xml;HDR=YES;IMEX=1;'"
        
        Using conn As New OleDbConnection(connectionString)
            conn.Open()
            
            ' الحصول على أسماء الأوراق
            Dim tables As DataTable = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)
            
            For Each row As DataRow In tables.Rows
                Dim sheetName As String = row("TABLE_NAME").ToString()
                
                If sheetName.EndsWith("$") Then
                    ' تحميل بيانات الورقة
                    Dim query As String = $"SELECT * FROM [{sheetName}]"
                    Using adapter As New OleDbDataAdapter(query, conn)
                        Dim dt As New DataTable()
                        adapter.Fill(dt)
                        
                        ' إزالة علامة $ من اسم الورقة
                        Dim cleanName As String = sheetName.Replace("$", "")
                        importedData(cleanName) = dt
                    End Using
                End If
            Next
        End Using
    End Sub

    Private Sub Button_Backup_Click(sender As Object, e As EventArgs) Handles Button_Backup.Click
        ' إنشاء نسخة احتياطية من قاعدة البيانات الحالية
        Try
            Label_Status.Text = "جاري إنشاء نسخة احتياطية..."
            ProgressBar1.Style = ProgressBarStyle.Marquee
            
            Dim backupPath As String = Path.Combine(Application.StartupPath, "Backup")
            If Not Directory.Exists(backupPath) Then
                Directory.CreateDirectory(backupPath)
            End If
            
            Dim backupFileName As String = $"Project_DB_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak"
            Dim fullBackupPath As String = Path.Combine(backupPath, backupFileName)
            
            Using conn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=master;Integrated Security=True")
                conn.Open()
                Dim backupQuery As String = $"BACKUP DATABASE [Project_DB] TO DISK = '{fullBackupPath}'"
                Using cmd As New SqlCommand(backupQuery, conn)
                    cmd.CommandTimeout = 300 ' 5 دقائق
                    cmd.ExecuteNonQuery()
                End Using
            End Using
            
            Label_Status.Text = $"تم إنشاء النسخة الاحتياطية: {backupFileName}"
            MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح في:{vbNewLine}{fullBackupPath}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            MessageBox.Show($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Label_Status.Text = "فشل في إنشاء النسخة الاحتياطية"
        Finally
            ProgressBar1.Style = ProgressBarStyle.Continuous
            ProgressBar1.Value = 0
        End Try
    End Sub

    Private Sub Button_Import_Click(sender As Object, e As EventArgs) Handles Button_Import.Click
        ' بدء عملية الاستيراد
        If importedData.Count = 0 Then
            MessageBox.Show("يرجى تحميل البيانات أولاً باستخدام زر المعاينة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        Dim result As DialogResult = MessageBox.Show(
            "هل أنت متأكد من بدء عملية الاستيراد؟" & vbNewLine & 
            "سيتم تعديل قاعدة البيانات الحالية." & vbNewLine & vbNewLine &
            "ننصح بإنشاء نسخة احتياطية أولاً.",
            "تأكيد الاستيراد", 
            MessageBoxButtons.YesNo, 
            MessageBoxIcon.Question)
        
        If result = DialogResult.Yes Then
            StartImportProcess()
        End If
    End Sub

    Private Sub StartImportProcess()
        ' بدء عملية الاستيراد الفعلية
        Try
            Label_Status.Text = "جاري استيراد البيانات..."
            ProgressBar1.Maximum = GetSelectedTablesCount()
            ProgressBar1.Value = 0
            
            Dim importedCount As Integer = 0
            Dim errorCount As Integer = 0
            
            ' استيراد الجداول المحددة
            If CheckBox_Users.Checked Then
                ImportTable("users_table", "المستخدمين", importedCount, errorCount)
            End If
            
            If CheckBox_Subscribers.Checked Then
                ImportTable("Subscribers_table", "المشتركين", importedCount, errorCount)
            End If
            
            If CheckBox_Family.Checked Then
                ImportTable("Family_table", "أفراد العائلة", importedCount, errorCount)
            End If
            
            If CheckBox_Donors.Checked Then
                ImportTable("Donors_table", "المتبرعين", importedCount, errorCount)
            End If
            
            If CheckBox_Items.Checked Then
                ImportTable("Item_table", "المواد", importedCount, errorCount)
            End If
            
            If CheckBox_Donations.Checked Then
                ImportTable("Donations_table", "التبرعات", importedCount, errorCount)
            End If
            
            If CheckBox_Needs.Checked Then
                ImportTable("Needs_table", "الاحتياجات", importedCount, errorCount)
            End If
            
            ' عرض النتائج
            Dim message As String = $"تم الانتهاء من الاستيراد:{vbNewLine}" &
                                  $"تم استيراد: {importedCount} جدول{vbNewLine}" &
                                  $"أخطاء: {errorCount} جدول"
            
            MessageBox.Show(message, "انتهى الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Label_Status.Text = "تم الانتهاء من الاستيراد"
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في عملية الاستيراد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Label_Status.Text = "فشل في الاستيراد"
        End Try
    End Sub

    Private Function GetSelectedTablesCount() As Integer
        Dim count As Integer = 0
        If CheckBox_Users.Checked Then count += 1
        If CheckBox_Subscribers.Checked Then count += 1
        If CheckBox_Family.Checked Then count += 1
        If CheckBox_Donors.Checked Then count += 1
        If CheckBox_Items.Checked Then count += 1
        If CheckBox_Donations.Checked Then count += 1
        If CheckBox_Needs.Checked Then count += 1
        Return count
    End Function

    Private Sub ImportTable(tableName As String, displayName As String, ByRef importedCount As Integer, ByRef errorCount As Integer)
        ' استيراد جدول محدد
        Try
            Label_Status.Text = $"جاري استيراد {displayName}..."

            ' البحث عن الجدول في البيانات المستوردة
            Dim sourceTable As DataTable = Nothing

            ' البحث بأسماء مختلفة محتملة
            Dim possibleNames() As String = {tableName, tableName.Replace("_table", ""), displayName}

            For Each possibleName As String In possibleNames
                If importedData.ContainsKey(possibleName) Then
                    sourceTable = importedData(possibleName)
                    Exit For
                End If
            Next

            If sourceTable Is Nothing OrElse sourceTable.Rows.Count = 0 Then
                Label_Status.Text = $"لم يتم العثور على بيانات لجدول {displayName}"
                ProgressBar1.Value += 1
                Return
            End If

            ' تنفيذ الاستيراد حسب الطريقة المحددة
            If RadioButton_Replace.Checked Then
                ' حذف البيانات الحالية أولاً
                ClearCurrentTable(tableName)
            End If

            ' استيراد البيانات الجديدة
            ImportTableData(tableName, sourceTable)

            importedCount += 1
            Label_Status.Text = $"تم استيراد {displayName} ({sourceTable.Rows.Count} سجل)"

        Catch ex As Exception
            errorCount += 1
            Label_Status.Text = $"خطأ في استيراد {displayName}: {ex.Message}"
            ' يمكن إضافة تسجيل الأخطاء هنا
        Finally
            ProgressBar1.Value += 1
        End Try
    End Sub

    Private Sub ClearCurrentTable(tableName As String)
        ' حذف البيانات الحالية من الجدول
        Try
            currentConn.Open()
            Dim deleteQuery As String = $"DELETE FROM {tableName}"
            Using cmd As New SqlCommand(deleteQuery, currentConn)
                cmd.ExecuteNonQuery()
            End Using
        Finally
            If currentConn.State = ConnectionState.Open Then
                currentConn.Close()
            End If
        End Try
    End Sub

    Private Sub ImportTableData(tableName As String, sourceTable As DataTable)
        ' استيراد البيانات إلى الجدول
        Try
            currentConn.Open()

            ' إنشاء استعلام الإدراج حسب نوع الجدول
            Select Case tableName
                Case "users_table"
                    ImportUsersTable(sourceTable)
                Case "Subscribers_table"
                    ImportSubscribersTable(sourceTable)
                Case "Family_table"
                    ImportFamilyTable(sourceTable)
                Case "Donors_table"
                    ImportDonorsTable(sourceTable)
                Case "Item_table"
                    ImportItemsTable(sourceTable)
                Case "Donations_table"
                    ImportDonationsTable(sourceTable)
                Case "Needs_table"
                    ImportNeedsTable(sourceTable)
            End Select

        Finally
            If currentConn.State = ConnectionState.Open Then
                currentConn.Close()
            End If
        End Try
    End Sub

    Private Sub ImportUsersTable(sourceTable As DataTable)
        ' استيراد جدول المستخدمين
        For Each row As DataRow In sourceTable.Rows
            Try
                Dim insertQuery As String = "INSERT INTO users_table (user_name, user_password, user_validity) VALUES (@name, @password, @validity)"
                Using cmd As New SqlCommand(insertQuery, currentConn)
                    ' محاولة قراءة الأعمدة بأسماء مختلفة محتملة
                    cmd.Parameters.AddWithValue("@name", GetColumnValue(row, {"user_name", "username", "اسم المستخدم", "name"}))
                    cmd.Parameters.AddWithValue("@password", GetColumnValue(row, {"user_password", "password", "كلمة المرور", "pass"}))
                    cmd.Parameters.AddWithValue("@validity", GetColumnValue(row, {"user_validity", "validity", "صلاحية", "admin"}, False))
                    cmd.ExecuteNonQuery()
                End Using
            Catch ex As Exception
                ' تخطي السجل في حالة الخطأ
                Continue For
            End Try
        Next
    End Sub

    Private Sub ImportSubscribersTable(sourceTable As DataTable)
        ' استيراد جدول المشتركين
        For Each row As DataRow In sourceTable.Rows
            Try
                Dim insertQuery As String = "INSERT INTO Subscribers_table (National_id, Nationality, National_number, Passport_number, Full_name, Age, Phone_number, Address, Employment_state, Work_p, Income_source, has_disease) VALUES (@nid, @nationality, @nnumber, @passport, @name, @age, @phone, @address, @employment, @work, @income, @disease)"
                Using cmd As New SqlCommand(insertQuery, currentConn)
                    cmd.Parameters.AddWithValue("@nid", GetColumnValue(row, {"National_id", "national_id", "رقم الهوية", "id"}))
                    cmd.Parameters.AddWithValue("@nationality", GetColumnValue(row, {"Nationality", "nationality", "الجنسية"}, "غير محدد"))
                    cmd.Parameters.AddWithValue("@nnumber", GetColumnValue(row, {"National_number", "national_number", "رقم الوطني"}))
                    cmd.Parameters.AddWithValue("@passport", GetColumnValue(row, {"Passport_number", "passport_number", "رقم الجواز"}))
                    cmd.Parameters.AddWithValue("@name", GetColumnValue(row, {"Full_name", "full_name", "الاسم الكامل", "name"}))
                    cmd.Parameters.AddWithValue("@age", GetColumnValue(row, {"Age", "age", "العمر"}, 0))
                    cmd.Parameters.AddWithValue("@phone", GetColumnValue(row, {"Phone_number", "phone_number", "رقم الهاتف", "phone"}))
                    cmd.Parameters.AddWithValue("@address", GetColumnValue(row, {"Address", "address", "العنوان"}))
                    cmd.Parameters.AddWithValue("@employment", GetColumnValue(row, {"Employment_state", "employment_state", "حالة العمل"}, "غير محدد"))
                    cmd.Parameters.AddWithValue("@work", GetColumnValue(row, {"Work_p", "work_p", "مكان العمل"}))
                    cmd.Parameters.AddWithValue("@income", GetColumnValue(row, {"Income_source", "income_source", "مصدر الدخل"}))
                    cmd.Parameters.AddWithValue("@disease", GetColumnValue(row, {"has_disease", "disease", "مرض"}, "لا"))
                    cmd.ExecuteNonQuery()
                End Using
            Catch ex As Exception
                Continue For
            End Try
        Next
    End Sub

    Private Function GetColumnValue(row As DataRow, columnNames() As String, Optional defaultValue As Object = Nothing) As Object
        ' البحث عن قيمة العمود بأسماء مختلفة
        For Each columnName As String In columnNames
            If row.Table.Columns.Contains(columnName) AndAlso Not IsDBNull(row(columnName)) Then
                Return row(columnName)
            End If
        Next

        ' إرجاع القيمة الافتراضية إذا لم يتم العثور على العمود
        If defaultValue IsNot Nothing Then
            Return defaultValue
        Else
            Return DBNull.Value
        End If
    End Function
End Class
