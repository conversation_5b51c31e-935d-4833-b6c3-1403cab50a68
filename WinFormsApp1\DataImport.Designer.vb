﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class DataImport
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Button_SelectFile = New System.Windows.Forms.Button()
        Me.TextBox_FilePath = New System.Windows.Forms.TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.RadioButton_Access = New System.Windows.Forms.RadioButton()
        Me.RadioButton_SqlServer = New System.Windows.Forms.RadioButton()
        Me.RadioButton_Excel = New System.Windows.Forms.RadioButton()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.CheckBox_Needs = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Donations = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Items = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Donors = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Family = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Subscribers = New System.Windows.Forms.CheckBox()
        Me.CheckBox_Users = New System.Windows.Forms.CheckBox()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.RadioButton_Replace = New System.Windows.Forms.RadioButton()
        Me.RadioButton_Merge = New System.Windows.Forms.RadioButton()
        Me.Button_Import = New System.Windows.Forms.Button()
        Me.Button_Preview = New System.Windows.Forms.Button()
        Me.ProgressBar1 = New System.Windows.Forms.ProgressBar()
        Me.Label_Status = New System.Windows.Forms.Label()
        Me.DataGridView_Preview = New System.Windows.Forms.DataGridView()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.Button_Backup = New System.Windows.Forms.Button()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.DataGridView_Preview, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Button_SelectFile)
        Me.GroupBox1.Controls.Add(Me.TextBox_FilePath)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(760, 80)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "اختيار ملف قاعدة البيانات"
        '
        'Button_SelectFile
        '
        Me.Button_SelectFile.Location = New System.Drawing.Point(15, 45)
        Me.Button_SelectFile.Name = "Button_SelectFile"
        Me.Button_SelectFile.Size = New System.Drawing.Size(100, 25)
        Me.Button_SelectFile.TabIndex = 2
        Me.Button_SelectFile.Text = "اختيار ملف"
        Me.Button_SelectFile.UseVisualStyleBackColor = True
        '
        'TextBox_FilePath
        '
        Me.TextBox_FilePath.Location = New System.Drawing.Point(130, 47)
        Me.TextBox_FilePath.Name = "TextBox_FilePath"
        Me.TextBox_FilePath.ReadOnly = True
        Me.TextBox_FilePath.Size = New System.Drawing.Size(615, 23)
        Me.TextBox_FilePath.TabIndex = 1
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(15, 25)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(200, 15)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "اختر ملف قاعدة البيانات من 2014:"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.RadioButton_Access)
        Me.GroupBox2.Controls.Add(Me.RadioButton_SqlServer)
        Me.GroupBox2.Controls.Add(Me.RadioButton_Excel)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 98)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(250, 100)
        Me.GroupBox2.TabIndex = 1
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "نوع قاعدة البيانات"
        '
        'RadioButton_Access
        '
        Me.RadioButton_Access.AutoSize = True
        Me.RadioButton_Access.Checked = True
        Me.RadioButton_Access.Location = New System.Drawing.Point(15, 25)
        Me.RadioButton_Access.Name = "RadioButton_Access"
        Me.RadioButton_Access.Size = New System.Drawing.Size(150, 19)
        Me.RadioButton_Access.TabIndex = 0
        Me.RadioButton_Access.TabStop = True
        Me.RadioButton_Access.Text = "Access (.mdb/.accdb)"
        Me.RadioButton_Access.UseVisualStyleBackColor = True
        '
        'RadioButton_SqlServer
        '
        Me.RadioButton_SqlServer.AutoSize = True
        Me.RadioButton_SqlServer.Location = New System.Drawing.Point(15, 50)
        Me.RadioButton_SqlServer.Name = "RadioButton_SqlServer"
        Me.RadioButton_SqlServer.Size = New System.Drawing.Size(130, 19)
        Me.RadioButton_SqlServer.TabIndex = 1
        Me.RadioButton_SqlServer.Text = "SQL Server (.mdf)"
        Me.RadioButton_SqlServer.UseVisualStyleBackColor = True
        '
        'RadioButton_Excel
        '
        Me.RadioButton_Excel.AutoSize = True
        Me.RadioButton_Excel.Location = New System.Drawing.Point(15, 75)
        Me.RadioButton_Excel.Name = "RadioButton_Excel"
        Me.RadioButton_Excel.Size = New System.Drawing.Size(100, 19)
        Me.RadioButton_Excel.TabIndex = 2
        Me.RadioButton_Excel.Text = "Excel (.xlsx)"
        Me.RadioButton_Excel.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.CheckBox_Needs)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Donations)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Items)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Donors)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Family)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Subscribers)
        Me.GroupBox3.Controls.Add(Me.CheckBox_Users)
        Me.GroupBox3.Location = New System.Drawing.Point(280, 98)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(250, 180)
        Me.GroupBox3.TabIndex = 2
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "الجداول المراد استيرادها"
        '
        'CheckBox_Needs
        '
        Me.CheckBox_Needs.AutoSize = True
        Me.CheckBox_Needs.Location = New System.Drawing.Point(15, 150)
        Me.CheckBox_Needs.Name = "CheckBox_Needs"
        Me.CheckBox_Needs.Size = New System.Drawing.Size(80, 19)
        Me.CheckBox_Needs.TabIndex = 6
        Me.CheckBox_Needs.Text = "الاحتياجات"
        Me.CheckBox_Needs.UseVisualStyleBackColor = True
        '
        'CheckBox_Donations
        '
        Me.CheckBox_Donations.AutoSize = True
        Me.CheckBox_Donations.Location = New System.Drawing.Point(15, 125)
        Me.CheckBox_Donations.Name = "CheckBox_Donations"
        Me.CheckBox_Donations.Size = New System.Drawing.Size(70, 19)
        Me.CheckBox_Donations.TabIndex = 5
        Me.CheckBox_Donations.Text = "التبرعات"
        Me.CheckBox_Donations.UseVisualStyleBackColor = True
        '
        'CheckBox_Items
        '
        Me.CheckBox_Items.AutoSize = True
        Me.CheckBox_Items.Location = New System.Drawing.Point(15, 100)
        Me.CheckBox_Items.Name = "CheckBox_Items"
        Me.CheckBox_Items.Size = New System.Drawing.Size(55, 19)
        Me.CheckBox_Items.TabIndex = 4
        Me.CheckBox_Items.Text = "المواد"
        Me.CheckBox_Items.UseVisualStyleBackColor = True
        '
        'CheckBox_Donors
        '
        Me.CheckBox_Donors.AutoSize = True
        Me.CheckBox_Donors.Location = New System.Drawing.Point(15, 75)
        Me.CheckBox_Donors.Name = "CheckBox_Donors"
        Me.CheckBox_Donors.Size = New System.Drawing.Size(75, 19)
        Me.CheckBox_Donors.TabIndex = 3
        Me.CheckBox_Donors.Text = "المتبرعين"
        Me.CheckBox_Donors.UseVisualStyleBackColor = True
        '
        'CheckBox_Family
        '
        Me.CheckBox_Family.AutoSize = True
        Me.CheckBox_Family.Location = New System.Drawing.Point(15, 50)
        Me.CheckBox_Family.Name = "CheckBox_Family"
        Me.CheckBox_Family.Size = New System.Drawing.Size(85, 19)
        Me.CheckBox_Family.TabIndex = 2
        Me.CheckBox_Family.Text = "أفراد العائلة"
        Me.CheckBox_Family.UseVisualStyleBackColor = True
        '
        'CheckBox_Subscribers
        '
        Me.CheckBox_Subscribers.AutoSize = True
        Me.CheckBox_Subscribers.Checked = True
        Me.CheckBox_Subscribers.CheckState = System.Windows.Forms.CheckState.Checked
        Me.CheckBox_Subscribers.Location = New System.Drawing.Point(15, 25)
        Me.CheckBox_Subscribers.Name = "CheckBox_Subscribers"
        Me.CheckBox_Subscribers.Size = New System.Drawing.Size(75, 19)
        Me.CheckBox_Subscribers.TabIndex = 1
        Me.CheckBox_Subscribers.Text = "المشتركين"
        Me.CheckBox_Subscribers.UseVisualStyleBackColor = True
        '
        'CheckBox_Users
        '
        Me.CheckBox_Users.AutoSize = True
        Me.CheckBox_Users.Location = New System.Drawing.Point(150, 25)
        Me.CheckBox_Users.Name = "CheckBox_Users"
        Me.CheckBox_Users.Size = New System.Drawing.Size(85, 19)
        Me.CheckBox_Users.TabIndex = 0
        Me.CheckBox_Users.Text = "المستخدمين"
        Me.CheckBox_Users.UseVisualStyleBackColor = True
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.RadioButton_Replace)
        Me.GroupBox4.Controls.Add(Me.RadioButton_Merge)
        Me.GroupBox4.Location = New System.Drawing.Point(550, 98)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(220, 80)
        Me.GroupBox4.TabIndex = 3
        Me.GroupBox4.TabStop = False
        Me.GroupBox4.Text = "طريقة الاستيراد"
        '
        'RadioButton_Replace
        '
        Me.RadioButton_Replace.AutoSize = True
        Me.RadioButton_Replace.Location = New System.Drawing.Point(15, 50)
        Me.RadioButton_Replace.Name = "RadioButton_Replace"
        Me.RadioButton_Replace.Size = New System.Drawing.Size(150, 19)
        Me.RadioButton_Replace.TabIndex = 1
        Me.RadioButton_Replace.Text = "استبدال البيانات الحالية"
        Me.RadioButton_Replace.UseVisualStyleBackColor = True
        '
        'RadioButton_Merge
        '
        Me.RadioButton_Merge.AutoSize = True
        Me.RadioButton_Merge.Checked = True
        Me.RadioButton_Merge.Location = New System.Drawing.Point(15, 25)
        Me.RadioButton_Merge.Name = "RadioButton_Merge"
        Me.RadioButton_Merge.Size = New System.Drawing.Size(140, 19)
        Me.RadioButton_Merge.TabIndex = 0
        Me.RadioButton_Merge.TabStop = True
        Me.RadioButton_Merge.Text = "دمج مع البيانات الحالية"
        Me.RadioButton_Merge.UseVisualStyleBackColor = True
        '
        'Button_Import
        '
        Me.Button_Import.BackColor = System.Drawing.Color.Green
        Me.Button_Import.ForeColor = System.Drawing.Color.White
        Me.Button_Import.Location = New System.Drawing.Point(550, 200)
        Me.Button_Import.Name = "Button_Import"
        Me.Button_Import.Size = New System.Drawing.Size(100, 35)
        Me.Button_Import.TabIndex = 4
        Me.Button_Import.Text = "بدء الاستيراد"
        Me.Button_Import.UseVisualStyleBackColor = False
        '
        'Button_Preview
        '
        Me.Button_Preview.Location = New System.Drawing.Point(670, 200)
        Me.Button_Preview.Name = "Button_Preview"
        Me.Button_Preview.Size = New System.Drawing.Size(100, 35)
        Me.Button_Preview.TabIndex = 5
        Me.Button_Preview.Text = "معاينة البيانات"
        Me.Button_Preview.UseVisualStyleBackColor = True
        '
        'ProgressBar1
        '
        Me.ProgressBar1.Location = New System.Drawing.Point(12, 290)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New System.Drawing.Size(760, 25)
        Me.ProgressBar1.TabIndex = 6
        '
        'Label_Status
        '
        Me.Label_Status.AutoSize = True
        Me.Label_Status.Location = New System.Drawing.Point(12, 270)
        Me.Label_Status.Name = "Label_Status"
        Me.Label_Status.Size = New System.Drawing.Size(50, 15)
        Me.Label_Status.TabIndex = 7
        Me.Label_Status.Text = "جاهز..."
        '
        'DataGridView_Preview
        '
        Me.DataGridView_Preview.AllowUserToAddRows = False
        Me.DataGridView_Preview.AllowUserToDeleteRows = False
        Me.DataGridView_Preview.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DataGridView_Preview.Location = New System.Drawing.Point(12, 330)
        Me.DataGridView_Preview.Name = "DataGridView_Preview"
        Me.DataGridView_Preview.ReadOnly = True
        Me.DataGridView_Preview.Size = New System.Drawing.Size(760, 250)
        Me.DataGridView_Preview.TabIndex = 8
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.Filter = "جميع الملفات المدعومة|*.mdb;*.accdb;*.mdf;*.xlsx|Access Database|*.mdb;*.accdb|S" &
    "QL Server|*.mdf|Excel Files|*.xlsx"
        Me.OpenFileDialog1.Title = "اختر ملف قاعدة البيانات"
        '
        'Button_Backup
        '
        Me.Button_Backup.BackColor = System.Drawing.Color.Orange
        Me.Button_Backup.ForeColor = System.Drawing.Color.White
        Me.Button_Backup.Location = New System.Drawing.Point(12, 200)
        Me.Button_Backup.Name = "Button_Backup"
        Me.Button_Backup.Size = New System.Drawing.Size(120, 35)
        Me.Button_Backup.TabIndex = 9
        Me.Button_Backup.Text = "نسخ احتياطي أولاً"
        Me.Button_Backup.UseVisualStyleBackColor = False
        '
        'DataImport
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 15.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(784, 592)
        Me.Controls.Add(Me.Button_Backup)
        Me.Controls.Add(Me.DataGridView_Preview)
        Me.Controls.Add(Me.Label_Status)
        Me.Controls.Add(Me.ProgressBar1)
        Me.Controls.Add(Me.Button_Preview)
        Me.Controls.Add(Me.Button_Import)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.GroupBox3)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.Name = "DataImport"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "استيراد البيانات من قاعدة البيانات السابقة"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.DataGridView_Preview, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents Button_SelectFile As Button
    Friend WithEvents TextBox_FilePath As TextBox
    Friend WithEvents Label1 As Label
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents RadioButton_Access As RadioButton
    Friend WithEvents RadioButton_SqlServer As RadioButton
    Friend WithEvents RadioButton_Excel As RadioButton
    Friend WithEvents GroupBox3 As GroupBox
    Friend WithEvents CheckBox_Needs As CheckBox
    Friend WithEvents CheckBox_Donations As CheckBox
    Friend WithEvents CheckBox_Items As CheckBox
    Friend WithEvents CheckBox_Donors As CheckBox
    Friend WithEvents CheckBox_Family As CheckBox
    Friend WithEvents CheckBox_Subscribers As CheckBox
    Friend WithEvents CheckBox_Users As CheckBox
    Friend WithEvents GroupBox4 As GroupBox
    Friend WithEvents RadioButton_Replace As RadioButton
    Friend WithEvents RadioButton_Merge As RadioButton
    Friend WithEvents Button_Import As Button
    Friend WithEvents Button_Preview As Button
    Friend WithEvents ProgressBar1 As ProgressBar
    Friend WithEvents Label_Status As Label
    Friend WithEvents DataGridView_Preview As DataGridView
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents Button_Backup As Button
End Class
