Imports System.Data.SqlClient
Imports System.Data

Public Class CreateDefaultUser
    Inherits Form

    Private WithEvents Button_CreateUser As Button
    Private WithEvents Button_Close As Button
    Private WithEvents Label_Status As Label

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Button_CreateUser = New Button()
        Me.Button_Close = New Button()
        Me.Label_Status = New Label()
        Me.SuspendLayout()

        ' Button_CreateUser
        Me.Button_CreateUser.BackColor = Color.Blue
        Me.Button_CreateUser.ForeColor = Color.White
        Me.Button_CreateUser.Location = New Point(50, 50)
        Me.Button_CreateUser.Name = "Button_CreateUser"
        Me.Button_CreateUser.Size = New Size(200, 40)
        Me.Button_CreateUser.Text = "إنشاء مستخدم افتراضي"
        Me.Button_CreateUser.UseVisualStyleBackColor = False

        ' Button_Close
        Me.Button_Close.Location = New Point(270, 50)
        Me.Button_Close.Name = "Button_Close"
        Me.Button_Close.Size = New Size(100, 40)
        Me.Button_Close.Text = "إغلاق"
        Me.Button_Close.UseVisualStyleBackColor = True

        ' Label_Status
        Me.Label_Status.AutoSize = True
        Me.Label_Status.Location = New Point(50, 110)
        Me.Label_Status.Name = "Label_Status"
        Me.Label_Status.Size = New Size(300, 60)
        Me.Label_Status.Text = "سيتم إنشاء مستخدم افتراضي:" & Environment.NewLine & 
                              "اسم المستخدم: admin" & Environment.NewLine & 
                              "كلمة المرور: 123" & Environment.NewLine & 
                              "الصلاحية: مدير"

        ' CreateDefaultUser
        Me.ClientSize = New Size(420, 200)
        Me.Controls.Add(Me.Button_CreateUser)
        Me.Controls.Add(Me.Button_Close)
        Me.Controls.Add(Me.Label_Status)
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "CreateDefaultUser"
        Me.RightToLeft = RightToLeft.Yes
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Text = "إنشاء مستخدم افتراضي"
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub

    Private Sub Button_CreateUser_Click(sender As Object, e As EventArgs) Handles Button_CreateUser.Click
        CreateUser()
    End Sub

    Private Sub Button_Close_Click(sender As Object, e As EventArgs) Handles Button_Close.Click
        Me.Close()
    End Sub

    Private Sub CreateUser()
        Try
            Dim connectionString As String = "Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=Project_DB;Integrated Security=True"
            
            Using conn As New SqlConnection(connectionString)
                conn.Open()
                
                ' التحقق من وجود المستخدم أولاً
                Dim checkQuery As String = "SELECT COUNT(*) FROM users_table WHERE user_name = @username"
                Using checkCmd As New SqlCommand(checkQuery, conn)
                    checkCmd.Parameters.AddWithValue("@username", "admin")
                    Dim count As Integer = Convert.ToInt32(checkCmd.ExecuteScalar())
                    
                    If count > 0 Then
                        Label_Status.Text = "المستخدم 'admin' موجود بالفعل!" & Environment.NewLine & 
                                          "يمكنك تسجيل الدخول باستخدام:" & Environment.NewLine & 
                                          "اسم المستخدم: admin" & Environment.NewLine & 
                                          "كلمة المرور: 123"
                        Label_Status.ForeColor = Color.Green
                        Return
                    End If
                End Using
                
                ' إنشاء المستخدم الجديد
                Dim insertQuery As String = "INSERT INTO users_table (user_name, user_password, user_validity) VALUES (@username, @password, @validity)"
                Using insertCmd As New SqlCommand(insertQuery, conn)
                    insertCmd.Parameters.AddWithValue("@username", "admin")
                    insertCmd.Parameters.AddWithValue("@password", "123")
                    insertCmd.Parameters.AddWithValue("@validity", True) ' مدير
                    
                    insertCmd.ExecuteNonQuery()
                End Using
            End Using
            
            Label_Status.Text = "تم إنشاء المستخدم بنجاح!" & Environment.NewLine & 
                              "يمكنك الآن تسجيل الدخول باستخدام:" & Environment.NewLine & 
                              "اسم المستخدم: admin" & Environment.NewLine & 
                              "كلمة المرور: 123"
            Label_Status.ForeColor = Color.Green
            
            MessageBox.Show("تم إنشاء المستخدم الافتراضي بنجاح!" & Environment.NewLine & Environment.NewLine & 
                          "اسم المستخدم: admin" & Environment.NewLine & 
                          "كلمة المرور: 123" & Environment.NewLine & 
                          "الصلاحية: مدير", 
                          "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            Label_Status.Text = "فشل في إنشاء المستخدم: " & ex.Message
            Label_Status.ForeColor = Color.Red
            MessageBox.Show("حدث خطأ أثناء إنشاء المستخدم:" & Environment.NewLine & ex.Message, 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
