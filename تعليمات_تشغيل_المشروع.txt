تعليمات تشغيل المشروع مع SQL Server 2021
==========================================

## ✅ **تم تحديث المشروع بنجاح!**

لقد قمت بتحديث جميع connection strings في المشروع لتعمل مع SQL Server 2021 الخاص بك:

**اسم الخادم الجديد:** `DESKTOP-803R29V` (SQL Server 2021)

---

## 📁 **الملفات التي تم تحديثها:**

1. ✅ `LOGIN_FORM.vb` - نموذج تسجيل الدخول
2. ✅ `Subscriber.vb` - إدارة المشتركين
3. ✅ `donor.vb` - إدارة المتبرعين
4. ✅ `family.vb` - إدارة أفراد العائلة
5. ✅ `Item.vb` - إدارة المواد
6. ✅ `Donation.vb` - إدارة التبرعات
7. ✅ `needs.vb` - إدارة الاحتياجات
8. ✅ `user_form.vb` - إدارة المستخدمين
9. ✅ `MedicalRecord.vb` - السجلات الطبية
10. ✅ `Report.vb` - التقارير

---

## 🚀 **الخطوات التالية:**

### **1. تأكد من تشغيل SQL Server 2021:**
- افتح "Services" في Windows
- ابحث عن "SQL Server (MSSQLSERVER)" أو "SQL Server"
- تأكد من أنه يعمل (Running)

### **2. قاعدة البيانات موجودة:**
✅ لديك بالفعل قاعدة البيانات `Project_DB` في SQL Server 2021
✅ المشروع الآن يتصل مباشرة بقاعدة البيانات الموجودة

### **3. إنشاء الجداول المطلوبة:**
ستحتاج لإنشاء الجداول التالية في قاعدة البيانات:

- `users_table` - جدول المستخدمين
- `Subscribers_table` - جدول المشتركين
- `Family_table` - جدول أفراد العائلة
- `Donors_table` - جدول المتبرعين
- `Item_table` - جدول المواد
- `Donations_table` - جدول التبرعات
- `Needs_table` - جدول الاحتياجات

### **4. إنشاء مستخدم افتراضي:**
أضف مستخدم افتراضي لتسجيل الدخول:

```sql
INSERT INTO users_table (user_name, user_password, user_validity) 
VALUES ('admin', '123', 1);
```

---

## 🔧 **تشغيل المشروع:**

1. **افتح Terminal/Command Prompt**
2. **انتقل لمجلد المشروع:**
   ```
   cd "C:\Users\<USER>\Downloads\Telegram Desktop\WinFormsAppNEW\WinFormsAppNEW\WinFormsApp1"
   ```
3. **شغل المشروع:**
   ```
   dotnet run
   ```

---

## 🔑 **تسجيل الدخول:**

بعد إنشاء المستخدم الافتراضي:
- **اسم المستخدم:** admin
- **كلمة المرور:** 123

---

## ⚠️ **في حالة ظهور أخطاء:**

### **خطأ الاتصال بقاعدة البيانات:**
- تأكد من تشغيل SQL Server 2021
- تأكد من صحة اسم الخادم: `DESKTOP-803R29V`
- تأكد من وجود قاعدة البيانات `Project_DB`

### **خطأ "الجدول غير موجود":**
- تأكد من إنشاء جميع الجداول المطلوبة
- تحقق من أسماء الجداول والأعمدة

### **خطأ "المستخدم غير موجود":**
- أنشئ مستخدم افتراضي في جدول `users_table`
- تأكد من صحة اسم المستخدم وكلمة المرور

---

## 📝 **ملاحظات مهمة:**

1. **النسخ الاحتياطي:** قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
2. **الأمان:** غير كلمة المرور الافتراضية بعد أول تسجيل دخول
3. **الصلاحيات:** تأكد من وجود صلاحيات كافية للوصول لقاعدة البيانات

---

## 🎉 **تم بنجاح!**

المشروع الآن جاهز للعمل على جهازك مع SQL Server 2021.

إذا واجهت أي مشاكل، تأكد من:
- ✅ تشغيل SQL Server 2021
- ✅ وجود قاعدة البيانات والجداول
- ✅ وجود مستخدم للدخول
- ✅ صحة connection string: `DESKTOP-803R29V`
