Imports System.Data.SqlClient
Imports System.Data
Imports System.IO

Public Class SimpleDataTransfer
    Inherits Form

    Private WithEvents Button_Transfer As Button
    Private WithEvents Button_Close As Button
    Private WithEvents Label_Status As Label
    Private WithEvents ProgressBar1 As ProgressBar

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Button_Transfer = New Button()
        Me.Button_Close = New Button()
        Me.Label_Status = New Label()
        Me.ProgressBar1 = New ProgressBar()
        Me.SuspendLayout()

        ' Button_Transfer
        Me.Button_Transfer.BackColor = Color.Green
        Me.Button_Transfer.ForeColor = Color.White
        Me.Button_Transfer.Location = New Point(50, 50)
        Me.Button_Transfer.Name = "Button_Transfer"
        Me.Button_Transfer.Size = New Size(200, 40)
        Me.Button_Transfer.Text = "نقل البيانات من مجلد p"
        Me.Button_Transfer.UseVisualStyleBackColor = False

        ' Button_Close
        Me.Button_Close.Location = New Point(270, 50)
        Me.Button_Close.Name = "Button_Close"
        Me.Button_Close.Size = New Size(100, 40)
        Me.Button_Close.Text = "إغلاق"
        Me.Button_Close.UseVisualStyleBackColor = True

        ' Label_Status
        Me.Label_Status.AutoSize = True
        Me.Label_Status.Location = New Point(50, 110)
        Me.Label_Status.Name = "Label_Status"
        Me.Label_Status.Size = New Size(100, 15)
        Me.Label_Status.Text = "جاهز لنقل البيانات"

        ' ProgressBar1
        Me.ProgressBar1.Location = New Point(50, 140)
        Me.ProgressBar1.Name = "ProgressBar1"
        Me.ProgressBar1.Size = New Size(320, 23)

        ' SimpleDataTransfer
        Me.ClientSize = New Size(420, 200)
        Me.Controls.Add(Me.Button_Transfer)
        Me.Controls.Add(Me.Button_Close)
        Me.Controls.Add(Me.Label_Status)
        Me.Controls.Add(Me.ProgressBar1)
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "SimpleDataTransfer"
        Me.RightToLeft = RightToLeft.Yes
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Text = "نقل البيانات"
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub

    Private Sub Button_Transfer_Click(sender As Object, e As EventArgs) Handles Button_Transfer.Click
        ' التأكد من رغبة المستخدم في النقل
        Dim result As DialogResult = MessageBox.Show(
            "هل أنت متأكد من نقل البيانات من مجلد p إلى قاعدة البيانات الحالية؟" & vbNewLine & vbNewLine &
            "سيتم استبدال البيانات الحالية بالبيانات الموجودة في مجلد p.",
            "تأكيد نقل البيانات",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question)

        If result = DialogResult.Yes Then
            TransferData()
        End If
    End Sub

    Private Sub Button_Close_Click(sender As Object, e As EventArgs) Handles Button_Close.Click
        Me.Close()
    End Sub

    Private Sub TransferData()
        Try
            Label_Status.Text = "جاري نقل البيانات..."
            ProgressBar1.Style = ProgressBarStyle.Marquee
            Button_Transfer.Enabled = False

            ' مسارات قواعد البيانات
            Dim sourcePath As String = Path.Combine(Application.StartupPath, "p\Project_DB.mdf")
            Dim targetConnectionString As String = "Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=Project_DB;Integrated Security=True"

            ' التحقق من وجود ملف المصدر
            If Not File.Exists(sourcePath) Then
                MessageBox.Show("لم يتم العثور على ملف قاعدة البيانات في مجلد p", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' اتصال قاعدة البيانات المصدر
            Dim sourceConnectionString As String = $"Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename={sourcePath};Integrated Security=True;Connect Timeout=30"

            ' قائمة الجداول للنقل
            Dim tables() As String = {"users_table", "Subscribers_table", "Family_table", "Donors_table", "Item_table", "Donations_table", "Needs_table"}
            Dim transferredCount As Integer = 0

            Using sourceConn As New SqlConnection(sourceConnectionString)
                Using targetConn As New SqlConnection(targetConnectionString)
                    sourceConn.Open()
                    targetConn.Open()

                    For Each tableName As String In tables
                        Try
                            Label_Status.Text = $"جاري نقل جدول {tableName}..."

                            ' جلب البيانات من المصدر
                            Dim sourceData As New DataTable()
                            Using adapter As New SqlDataAdapter($"SELECT * FROM {tableName}", sourceConn)
                                adapter.Fill(sourceData)
                            End Using

                            If sourceData.Rows.Count > 0 Then
                                ' حذف البيانات الحالية
                                Using deleteCmd As New SqlCommand($"DELETE FROM {tableName}", targetConn)
                                    deleteCmd.ExecuteNonQuery()
                                End Using

                                ' إدراج البيانات الجديدة
                                Using bulkCopy As New SqlBulkCopy(targetConn)
                                    bulkCopy.DestinationTableName = tableName
                                    bulkCopy.WriteToServer(sourceData)
                                End Using

                                transferredCount += 1
                            End If

                        Catch ex As Exception
                            ' تسجيل الخطأ والمتابعة
                            Console.WriteLine($"خطأ في نقل جدول {tableName}: {ex.Message}")
                        End Try
                    Next
                End Using
            End Using

            Label_Status.Text = $"تم نقل {transferredCount} جدول بنجاح"
            MessageBox.Show($"تم نقل البيانات بنجاح!{vbNewLine}تم نقل {transferredCount} جدول من أصل {tables.Length}", "نجح النقل", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            Label_Status.Text = "فشل في نقل البيانات"
            MessageBox.Show($"حدث خطأ أثناء نقل البيانات:{vbNewLine}{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ProgressBar1.Style = ProgressBarStyle.Continuous
            ProgressBar1.Value = 0
            Button_Transfer.Enabled = True
        End Try
    End Sub
End Class
