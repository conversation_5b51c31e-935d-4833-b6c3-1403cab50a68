Imports System.Data.SqlClient
Imports System.Data
Imports System.IO

Public Class DatabaseFixer
    Inherits Form

    Private WithEvents Button_CheckDB As Button
    Private WithEvents Button_CreateDB As Button
    Private WithEvents Button_CreateTables As Button
    Private WithEvents Button_FixConnection As Button
    Private WithEvents Button_Close As Button
    Private WithEvents Label_Status As Label
    Private WithEvents TextBox_Log As TextBox

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Button_CheckDB = New Button()
        Me.Button_CreateDB = New Button()
        Me.Button_CreateTables = New Button()
        Me.Button_FixConnection = New Button()
        Me.Button_Close = New Button()
        Me.Label_Status = New Label()
        Me.TextBox_Log = New TextBox()
        Me.SuspendLayout()

        ' Button_CheckDB
        Me.Button_CheckDB.BackColor = Color.Blue
        Me.Button_CheckDB.ForeColor = Color.White
        Me.Button_CheckDB.Location = New Point(20, 20)
        Me.Button_CheckDB.Size = New Size(150, 35)
        Me.Button_CheckDB.Text = "فحص قاعدة البيانات"
        Me.Button_CheckDB.UseVisualStyleBackColor = False

        ' Button_CreateDB
        Me.Button_CreateDB.BackColor = Color.Green
        Me.Button_CreateDB.ForeColor = Color.White
        Me.Button_CreateDB.Location = New Point(180, 20)
        Me.Button_CreateDB.Size = New Size(150, 35)
        Me.Button_CreateDB.Text = "إنشاء قاعدة البيانات"
        Me.Button_CreateDB.UseVisualStyleBackColor = False

        ' Button_CreateTables
        Me.Button_CreateTables.BackColor = Color.Orange
        Me.Button_CreateTables.ForeColor = Color.White
        Me.Button_CreateTables.Location = New Point(340, 20)
        Me.Button_CreateTables.Size = New Size(150, 35)
        Me.Button_CreateTables.Text = "إنشاء الجداول"
        Me.Button_CreateTables.UseVisualStyleBackColor = False

        ' Button_FixConnection
        Me.Button_FixConnection.BackColor = Color.Purple
        Me.Button_FixConnection.ForeColor = Color.White
        Me.Button_FixConnection.Location = New Point(500, 20)
        Me.Button_FixConnection.Size = New Size(150, 35)
        Me.Button_FixConnection.Text = "إصلاح الاتصال"
        Me.Button_FixConnection.UseVisualStyleBackColor = False

        ' Button_Close
        Me.Button_Close.Location = New Point(580, 400)
        Me.Button_Close.Size = New Size(70, 30)
        Me.Button_Close.Text = "إغلاق"
        Me.Button_Close.UseVisualStyleBackColor = True

        ' Label_Status
        Me.Label_Status.AutoSize = True
        Me.Label_Status.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        Me.Label_Status.Location = New Point(20, 70)
        Me.Label_Status.Size = New Size(200, 20)
        Me.Label_Status.Text = "جاهز لفحص قاعدة البيانات"

        ' TextBox_Log
        Me.TextBox_Log.Location = New Point(20, 100)
        Me.TextBox_Log.Multiline = True
        Me.TextBox_Log.ReadOnly = True
        Me.TextBox_Log.ScrollBars = ScrollBars.Vertical
        Me.TextBox_Log.Size = New Size(630, 290)
        Me.TextBox_Log.Font = New Font("Consolas", 9)

        ' DatabaseFixer
        Me.ClientSize = New Size(670, 450)
        Me.Controls.Add(Me.Button_CheckDB)
        Me.Controls.Add(Me.Button_CreateDB)
        Me.Controls.Add(Me.Button_CreateTables)
        Me.Controls.Add(Me.Button_FixConnection)
        Me.Controls.Add(Me.Button_Close)
        Me.Controls.Add(Me.Label_Status)
        Me.Controls.Add(Me.TextBox_Log)
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "DatabaseFixer"
        Me.RightToLeft = RightToLeft.Yes
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Text = "أداة إصلاح قاعدة البيانات"
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub

    Private Sub Button_CheckDB_Click(sender As Object, e As EventArgs) Handles Button_CheckDB.Click
        CheckDatabase()
    End Sub

    Private Sub Button_CreateDB_Click(sender As Object, e As EventArgs) Handles Button_CreateDB.Click
        CreateDatabase()
    End Sub

    Private Sub Button_CreateTables_Click(sender As Object, e As EventArgs) Handles Button_CreateTables.Click
        CreateTables()
    End Sub

    Private Sub Button_FixConnection_Click(sender As Object, e As EventArgs) Handles Button_FixConnection.Click
        FixConnection()
    End Sub

    Private Sub Button_Close_Click(sender As Object, e As EventArgs) Handles Button_Close.Click
        Me.Close()
    End Sub

    Private Sub LogMessage(message As String)
        TextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}{Environment.NewLine}")
        TextBox_Log.ScrollToCaret()
        Application.DoEvents()
    End Sub

    Private Sub CheckDatabase()
        Label_Status.Text = "جاري فحص قاعدة البيانات..."
        TextBox_Log.Clear()
        LogMessage("بدء فحص قاعدة البيانات...")

        Try
            ' فحص الاتصال بـ SQL Server
            LogMessage("فحص الاتصال بـ SQL Server...")
            Using masterConn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=master;Integrated Security=True")
                masterConn.Open()
                LogMessage("✅ الاتصال بـ SQL Server نجح")
                masterConn.Close()
            End Using

            ' فحص وجود قاعدة البيانات
            LogMessage("فحص وجود قاعدة البيانات Project_DB...")
            Using masterConn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=master;Integrated Security=True")
                masterConn.Open()
                Dim checkDbQuery As String = "SELECT COUNT(*) FROM sys.databases WHERE name = 'Project_DB'"
                Using cmd As New SqlCommand(checkDbQuery, masterConn)
                    Dim dbExists As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                    If dbExists > 0 Then
                        LogMessage("✅ قاعدة البيانات Project_DB موجودة")
                        
                        ' فحص الجداول
                        CheckTables()
                    Else
                        LogMessage("❌ قاعدة البيانات Project_DB غير موجودة")
                        LogMessage("💡 اضغط 'إنشاء قاعدة البيانات' لإنشائها")
                    End If
                End Using
            End Using

        Catch ex As Exception
            LogMessage($"❌ خطأ في فحص قاعدة البيانات: {ex.Message}")
            LogMessage("💡 جرب 'إصلاح الاتصال' أو تأكد من تشغيل SQL Server")
        End Try

        Label_Status.Text = "انتهى الفحص"
    End Sub

    Private Sub CheckTables()
        Try
            LogMessage("فحص الجداول في قاعدة البيانات...")
            Using conn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=Project_DB;Integrated Security=True")
                conn.Open()
                
                Dim tables() As String = {"users_table", "Subscribers_table", "Family_table", "Donors_table", "Item_table", "Donations_table", "Needs_table"}
                Dim missingTables As New List(Of String)
                
                For Each tableName As String In tables
                    Dim checkTableQuery As String = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName"
                    Using cmd As New SqlCommand(checkTableQuery, conn)
                        cmd.Parameters.AddWithValue("@tableName", tableName)
                        Dim tableExists As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                        If tableExists > 0 Then
                            LogMessage($"✅ الجدول {tableName} موجود")
                        Else
                            LogMessage($"❌ الجدول {tableName} مفقود")
                            missingTables.Add(tableName)
                        End If
                    End Using
                Next
                
                If missingTables.Count > 0 Then
                    LogMessage($"💡 يوجد {missingTables.Count} جدول مفقود. اضغط 'إنشاء الجداول'")
                Else
                    LogMessage("✅ جميع الجداول موجودة")
                End If
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في فحص الجداول: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateDatabase()
        Label_Status.Text = "جاري إنشاء قاعدة البيانات..."
        LogMessage("بدء إنشاء قاعدة البيانات...")

        Try
            Using masterConn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=master;Integrated Security=True")
                masterConn.Open()
                
                ' التحقق من وجود قاعدة البيانات أولاً
                Dim checkQuery As String = "SELECT COUNT(*) FROM sys.databases WHERE name = 'Project_DB'"
                Using checkCmd As New SqlCommand(checkQuery, masterConn)
                    Dim exists As Integer = Convert.ToInt32(checkCmd.ExecuteScalar())
                    If exists > 0 Then
                        LogMessage("⚠️ قاعدة البيانات موجودة بالفعل")
                        Return
                    End If
                End Using
                
                ' إنشاء قاعدة البيانات
                Dim createDbQuery As String = "CREATE DATABASE Project_DB"
                Using cmd As New SqlCommand(createDbQuery, masterConn)
                    cmd.ExecuteNonQuery()
                    LogMessage("✅ تم إنشاء قاعدة البيانات Project_DB بنجاح")
                End Using
            End Using
            
            Label_Status.Text = "تم إنشاء قاعدة البيانات بنجاح"
            LogMessage("💡 الآن يمكنك إنشاء الجداول")
            
        Catch ex As Exception
            LogMessage($"❌ فشل في إنشاء قاعدة البيانات: {ex.Message}")
            Label_Status.Text = "فشل في إنشاء قاعدة البيانات"
        End Try
    End Sub

    Private Sub CreateTables()
        Label_Status.Text = "جاري إنشاء الجداول..."
        LogMessage("بدء إنشاء الجداول...")

        Try
            Using conn As New SqlConnection("Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=Project_DB;Integrated Security=True")
                conn.Open()
                
                ' إنشاء جدول المستخدمين
                CreateUsersTable(conn)
                
                ' إنشاء جدول المشتركين
                CreateSubscribersTable(conn)
                
                ' إنشاء جدول العائلة
                CreateFamilyTable(conn)
                
                ' إنشاء جدول المتبرعين
                CreateDonorsTable(conn)
                
                ' إنشاء جدول المواد
                CreateItemsTable(conn)
                
                ' إنشاء جدول التبرعات
                CreateDonationsTable(conn)
                
                ' إنشاء جدول الاحتياجات
                CreateNeedsTable(conn)
                
                LogMessage("✅ تم إنشاء جميع الجداول بنجاح")
                Label_Status.Text = "تم إنشاء الجداول بنجاح"
            End Using
            
        Catch ex As Exception
            LogMessage($"❌ فشل في إنشاء الجداول: {ex.Message}")
            Label_Status.Text = "فشل في إنشاء الجداول"
        End Try
    End Sub

    Private Sub CreateUsersTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE users_table (
                    user_id INT IDENTITY(1,1) PRIMARY KEY,
                    user_name NVARCHAR(50) NOT NULL UNIQUE,
                    user_password NVARCHAR(50) NOT NULL,
                    user_validity BIT NOT NULL DEFAULT 0
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول المستخدمين")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول المستخدمين: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateSubscribersTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Subscribers_table (
                    Subscriber_id INT IDENTITY(1,1) PRIMARY KEY,
                    National_id NVARCHAR(20) UNIQUE,
                    Nationality NVARCHAR(50),
                    National_number NVARCHAR(20),
                    Passport_number NVARCHAR(20),
                    Full_name NVARCHAR(100) NOT NULL,
                    Age INT,
                    Phone_number NVARCHAR(20),
                    Address NVARCHAR(200),
                    Employment_state NVARCHAR(50),
                    Work_p NVARCHAR(100),
                    Income_source NVARCHAR(100),
                    has_disease NVARCHAR(10)
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول المشتركين")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول المشتركين: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateFamilyTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Family_table (
                    Family_id INT IDENTITY(1,1) PRIMARY KEY,
                    Subscriber_id INT,
                    Name NVARCHAR(100) NOT NULL,
                    Age INT,
                    Relationship NVARCHAR(50),
                    Disease_id NVARCHAR(100),
                    Employmentstatus NVARCHAR(50),
                    FOREIGN KEY (Subscriber_id) REFERENCES Subscribers_table(Subscriber_id)
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول العائلة")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول العائلة: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateDonorsTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Donors_table (
                    Donor_id INT IDENTITY(1,1) PRIMARY KEY,
                    DonorName NVARCHAR(100) NOT NULL,
                    PhoneNumber NVARCHAR(20),
                    DNational_id NVARCHAR(20)
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول المتبرعين")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول المتبرعين: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateItemsTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Item_table (
                    Item_id INT IDENTITY(1,1) PRIMARY KEY,
                    Item_name NVARCHAR(100) NOT NULL,
                    Item_quantity INT DEFAULT 0,
                    Item_category NVARCHAR(50),
                    Expir_date DATE
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول المواد")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول المواد: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateDonationsTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Donations_table (
                    Donation_id INT IDENTITY(1,1) PRIMARY KEY,
                    Donor_id INT,
                    Item_id INT,
                    Donation_type NVARCHAR(50),
                    quantity INT DEFAULT 0,
                    Donation_date DATE,
                    Donation_method NVARCHAR(50),
                    FOREIGN KEY (Donor_id) REFERENCES Donors_table(Donor_id),
                    FOREIGN KEY (Item_id) REFERENCES Item_table(Item_id)
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول التبرعات")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول التبرعات: {ex.Message}")
        End Try
    End Sub

    Private Sub CreateNeedsTable(conn As SqlConnection)
        Try
            Dim createTableQuery As String = "
                CREATE TABLE Needs_table (
                    Need_id INT IDENTITY(1,1) PRIMARY KEY,
                    Subscriber_id INT,
                    Item_id INT,
                    need_type NVARCHAR(50),
                    FamilyNumbe INT DEFAULT 0,
                    FOREIGN KEY (Subscriber_id) REFERENCES Subscribers_table(Subscriber_id),
                    FOREIGN KEY (Item_id) REFERENCES Item_table(Item_id)
                )"
            Using cmd As New SqlCommand(createTableQuery, conn)
                cmd.ExecuteNonQuery()
                LogMessage("✅ تم إنشاء جدول الاحتياجات")
            End Using
        Catch ex As Exception
            LogMessage($"❌ خطأ في إنشاء جدول الاحتياجات: {ex.Message}")
        End Try
    End Sub

    Private Sub FixConnection()
        Label_Status.Text = "جاري إصلاح الاتصال..."
        LogMessage("بدء إصلاح مشاكل الاتصال...")

        Try
            ' فحص خدمة SQL Server
            LogMessage("فحص خدمة SQL Server...")

            ' محاولة الاتصال بطرق مختلفة
            Dim connectionStrings() As String = {
                "Data Source=DESKTOP-OA3F4SP\SQLEXPRESS;Initial Catalog=master;Integrated Security=True",
                "Data Source=.\SQLEXPRESS;Initial Catalog=master;Integrated Security=True",
                "Data Source=(local)\SQLEXPRESS;Initial Catalog=master;Integrated Security=True",
                "Data Source=localhost\SQLEXPRESS;Initial Catalog=master;Integrated Security=True"
            }

            Dim workingConnection As String = Nothing

            For Each connStr As String In connectionStrings
                Try
                    Using testConn As New SqlConnection(connStr)
                        testConn.Open()
                        testConn.Close()
                        workingConnection = connStr
                        LogMessage($"✅ الاتصال نجح مع: {connStr}")
                        Exit For
                    End Using
                Catch
                    LogMessage($"❌ فشل الاتصال مع: {connStr}")
                End Try
            Next

            If workingConnection IsNot Nothing Then
                LogMessage("💡 تم العثور على اتصال يعمل")
                LogMessage("💡 يمكنك الآن المتابعة مع فحص قاعدة البيانات")
            Else
                LogMessage("❌ فشل في جميع محاولات الاتصال")
                LogMessage("💡 تأكد من:")
                LogMessage("   - تشغيل خدمة SQL Server Express")
                LogMessage("   - صحة اسم الخادم")
                LogMessage("   - تفعيل Named Pipes و TCP/IP")
            End If

        Catch ex As Exception
            LogMessage($"❌ خطأ في إصلاح الاتصال: {ex.Message}")
        End Try

        Label_Status.Text = "انتهى إصلاح الاتصال"
    End Sub
End Class
