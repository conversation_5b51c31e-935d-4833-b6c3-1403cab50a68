تعليمات استخدام أداة نقل البيانات
=====================================

الهدف:
-------
نقل البيانات من قاعدة البيانات الموجودة في مجلد "p" (التي جاءت مع المشروع من زميلك) 
إلى قاعدة البيانات على جهازك الحالي.

خطوات الاستخدام:
-----------------

1. تسجيل الدخول:
   - افتح التطبيق وسجل دخولك بحساب المدير أو الموظف
   - ستظهر جميع الأزرار بما في ذلك زر "نقل البيانات" الأزرق

2. فتح أداة نقل البيانات:
   - اضغط على زر "نقل البيانات" الأزرق في الشاشة الرئيسية
   - ستفتح نافذة جديدة لنقل البيانات

3. فحص قواعد البيانات:
   - ستقوم الأداة تلقائياً بفحص:
     * قاعدة البيانات المصدر (p\Project_DB.mdf)
     * قاعدة البيانات الهدف (جهازك الحالي)
   - تأكد من ظهور علامة ✓ خضراء للاتصالين

4. اختبار الاتصال (اختياري):
   - اضغط على "اختبار الاتصال" للتأكد من عمل كل شيء بشكل صحيح
   - ستظهر رسالة تعرض عدد السجلات في كل قاعدة بيانات

5. إنشاء نسخة احتياطية (مهم جداً):
   - اضغط على "نسخ احتياطي أولاً" قبل بدء النقل
   - سيتم حفظ النسخة الاحتياطية في مجلد Backup
   - هذا يحميك من فقدان البيانات في حالة حدوث خطأ

6. معاينة البيانات (اختياري):
   - اضغط على "معاينة البيانات" لرؤية عينة من البيانات المراد نقلها
   - ستظهر البيانات في الجدول أسفل النافذة

7. اختيار الجداول:
   - حدد الجداول التي تريد نقلها (افتراضياً جميع الجداول محددة)
   - يمكنك استخدام "تحديد الكل" لتحديد أو إلغاء تحديد جميع الجداول

8. اختيار طريقة النقل:
   - "استبدال البيانات الحالية": يحذف البيانات الموجودة ويضع الجديدة
   - "دمج مع البيانات الحالية": يضيف البيانات الجديدة للموجودة

9. بدء النقل:
   - اضغط على "بدء نقل البيانات" الأخضر
   - ستظهر رسالة تأكيد، اضغط "نعم" للمتابعة
   - راقب شريط التقدم والحالة

10. النتائج:
    - ستظهر رسالة في النهاية تعرض:
      * عدد الجداول التي تم نقلها بنجاح
      * عدد الجداول التي فشل نقلها (إن وجدت)

ملاحظات مهمة:
--------------

• تأكد من إغلاق جميع النوافذ الأخرى في التطبيق قبل النقل
• لا تغلق التطبيق أثناء عملية النقل
• في حالة فشل النقل، يمكنك استعادة النسخة الاحتياطية
• يُنصح بتجربة النقل على نسخة تجريبية أولاً

ترتيب نقل الجداول:
------------------
1. المستخدمين (users_table)
2. المشتركين (Subscribers_table)  
3. أفراد العائلة (Family_table)
4. المتبرعين (Donors_table)
5. المواد (Item_table)
6. التبرعات (Donations_table)
7. الاحتياجات (Needs_table)

هذا الترتيب مهم للحفاظ على العلاقات بين الجداول.

في حالة المشاكل:
-----------------
• تأكد من أن ملف p\Project_DB.mdf موجود
• تأكد من أن SQL Server Express يعمل على جهازك
• تأكد من صحة اسم الخادم في connection string
• راجع رسائل الخطأ للحصول على تفاصيل أكثر

للدعم:
-------
في حالة واجهت أي مشاكل، احتفظ برسائل الخطأ وتواصل مع المطور.
