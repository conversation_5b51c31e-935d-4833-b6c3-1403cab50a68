﻿Imports System.Data.SqlClient

Public Class Needs


    Dim conn As New SqlConnection("Data Source=DESKTOP-803R29V;Initial Catalog=Project_DB;Integrated Security=True")

    Private Sub Needs_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadNeedsFromSubscribers()
    End Sub

    Public Sub LoadNeedsFromSubscribers()
        Dim dt As New DataTable()
        Try
            conn.Open()
            ' استعلام محسن لعرض الاحتياجات بشكل صحيح
            Dim query As String = "
SELECT DISTINCT
    s.Subscriber_id AS SubscriberID,
    s.Full_name AS FullName,
    s.Family_number AS FamilyCount,
    COALESCE(n.Need_type, 'غير محدد') AS ItemType,
    COALESCE(i.Item_name, 'غير محدد') AS ItemName,
    COALESCE(i.Item_quantity, 0) AS Quantity,
    'مشترك' AS PersonType,
    n.Need_id AS NeedID
FROM Subscribers_table s
LEFT JOIN Needs_table n ON n.Subscriber_id = s.Subscriber_id
LEFT JOIN Item_table i ON i.Item_id = n.Item_id
WHERE s.Subscriber_id IS NOT NULL

UNION ALL

SELECT DISTINCT
    f.Subscriber_id AS SubscriberID,
    f.Name AS FullName,
    CAST(s.Family_number AS VARCHAR) AS FamilyCount,
    COALESCE(n.Need_type, 'غير محدد') AS ItemType,
    COALESCE(i.Item_name, 'غير محدد') AS ItemName,
    COALESCE(i.Item_quantity, 0) AS Quantity,
    'فرد عائلة' AS PersonType,
    n.Need_id AS NeedID
FROM Family_table f
LEFT JOIN Subscribers_table s ON s.Subscriber_id = f.Subscriber_id
LEFT JOIN Needs_table n ON n.Subscriber_id = f.Subscriber_id
LEFT JOIN Item_table i ON i.Item_id = n.Item_id
WHERE f.Subscriber_id IS NOT NULL

ORDER BY FullName;"

            Dim adapter As New SqlDataAdapter(query, conn)
            adapter.Fill(dt)
            conn.Close()

            DataGridView_need.DataSource = dt

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ في تحميل البيانات: " & ex.Message)
        End Try
    End Sub



    Private Sub DataGridView_need_Click(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView_need.CellContentClick

        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = DataGridView_need.Rows(e.RowIndex)
            TextBox_sub_name.Text = row.Cells("FullName").Value.ToString()
            family_relate.Text = row.Cells("FamilyCount").Value.ToString()
            TextBox_item_type.Text = row.Cells("ItemType").Value.ToString()
            TextBox_item_name.Text = row.Cells("ItemName").Value.ToString()
            TextBox_quantity.Text = row.Cells("Quantity").Value.ToString()

        End If
    End Sub

    Private Sub Button_save_Click(sender As Object, e As EventArgs) Handles Button_save.Click
        Try
            ' التحقق من الحقول
            If String.IsNullOrWhiteSpace(TextBox_sub_name.Text) Or String.IsNullOrWhiteSpace(TextBox_item_name.Text) Then
                MessageBox.Show("يرجى تعبئة جميع الحقول المطلوبة.")
                Exit Sub
            End If

            ' التحقق من صحة عدد أفراد العائلة
            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) OrElse familyCount < 0 Then
                MessageBox.Show("يرجى إدخال عدد صحيح لأفراد العائلة.")
                Exit Sub
            End If

            conn.Open()

            ' 🔹 جلب رقم المشترك بناءً على الاسم
            Dim subscriberId As Integer = 0
            Dim getSubIdCmd As New SqlCommand("SELECT Subscriber_id FROM Subscribers_table WHERE Full_name = @name", conn)
            getSubIdCmd.Parameters.AddWithValue("@name", TextBox_sub_name.Text.Trim())
            Dim resultSub = getSubIdCmd.ExecuteScalar()

            If resultSub Is Nothing Then
                ' البحث في جدول العائلة
                Dim getFamIdCmd As New SqlCommand("SELECT Subscriber_id FROM Family_table WHERE Name = @name", conn)
                getFamIdCmd.Parameters.AddWithValue("@name", TextBox_sub_name.Text.Trim())
                resultSub = getFamIdCmd.ExecuteScalar()

                If resultSub Is Nothing Then
                    conn.Close()
                    MessageBox.Show("❌ الشخص غير موجود في قاعدة البيانات.")
                    Exit Sub
                End If
            End If

            subscriberId = Convert.ToInt32(resultSub)






            ' 🔹 التحقق من وجود المادة أو إنشاؤها
            Dim itemId As Integer = 0
            Dim getItemIdCmd As New SqlCommand("SELECT TOP 1 Item_id FROM Item_table WHERE Item_name = @name AND Item_category = @category", conn)
            getItemIdCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
            getItemIdCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
            Dim resultItem = getItemIdCmd.ExecuteScalar()

            If resultItem Is Nothing Then
                ' إنشاء مادة جديدة
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim createItemCmd As New SqlCommand("INSERT INTO Item_table (Item_name, Item_category, Item_quantity, Expir_date) OUTPUT INSERTED.Item_id VALUES (@name, @category, @quantity, @date)", conn)
                createItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                createItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                createItemCmd.Parameters.AddWithValue("@quantity", quantity)
                createItemCmd.Parameters.AddWithValue("@date", DateTime.Today)
                itemId = Convert.ToInt32(createItemCmd.ExecuteScalar())
            Else
                itemId = Convert.ToInt32(resultItem)
            End If

            ' 🔹 التحقق من عدم وجود احتياج مكرر
            Dim checkDuplicateCmd As New SqlCommand("SELECT COUNT(*) FROM Needs_table WHERE Subscriber_id = @subId AND Item_id = @itemId", conn)
            checkDuplicateCmd.Parameters.AddWithValue("@subId", subscriberId)
            checkDuplicateCmd.Parameters.AddWithValue("@itemId", itemId)
            Dim duplicateCount As Integer = Convert.ToInt32(checkDuplicateCmd.ExecuteScalar())

            If duplicateCount > 0 Then
                conn.Close()
                MessageBox.Show("⚠️ هذا الاحتياج موجود بالفعل لهذا الشخص.")
                Exit Sub
            End If

            ' 🔹 إضافة الاحتياج إلى جدول Needs_table
            Dim insertCmd As New SqlCommand("INSERT INTO Needs_table (Subscriber_id, Item_id, Need_type, FamilyNumbe) VALUES (@subId, @itemId, @type, @family)", conn)
            insertCmd.Parameters.AddWithValue("@subId", subscriberId)
            insertCmd.Parameters.AddWithValue("@itemId", itemId)
            insertCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            insertCmd.Parameters.AddWithValue("@family", familyCount)
            insertCmd.ExecuteNonQuery()

            conn.Close()

            MessageBox.Show("✔️ تم حفظ الاحتياج بنجاح.")

            ' تصفير الحقول
            ClearFields()
            LoadNeedsFromSubscribers()

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء الحفظ: " & ex.Message)
        End Try
    End Sub

    ' دالة تصفير الحقول
    Private Sub ClearFields()
        TextBox_sub_name.Clear()
        family_relate.Clear()
        TextBox_item_type.Clear()
        TextBox_item_name.Clear()
        TextBox_quantity.Clear()
    End Sub

    Private Sub Butt_delete_Click(sender As Object, e As EventArgs) Handles Butt_delete.Click

        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لحذفه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)
        Dim subscriberName As String = selectedRow.Cells("FullName").Value.ToString()

        If MessageBox.Show("هل أنت متأكد من حذف هذا الاحتياج ؟", "تأكيد", MessageBoxButtons.YesNo) = DialogResult.Yes Then
            Try
                conn.Open()

                ' الحصول على رقم المشترك من الاسم
                Dim getSubIdCmd As New SqlCommand("SELECT Subscriber_id FROM Subscribers_table WHERE Full_name = @name", conn)
                getSubIdCmd.Parameters.AddWithValue("@name", subscriberName)
                Dim result = getSubIdCmd.ExecuteScalar()

                If result Is Nothing Then
                    conn.Close()
                    MessageBox.Show("❌ لا يمكن العثور على هذا المشترك.")
                    Return
                End If

                Dim subscriberId As Integer = Convert.ToInt32(result)

                ' حذف  من جدول الاحتياج
                Dim deleteNeedCmd As New SqlCommand("DELETE FROM Needs_table WHERE Subscriber_id = @subId", conn)
                deleteNeedCmd.Parameters.AddWithValue("@subId", subscriberId)
                deleteNeedCmd.ExecuteNonQuery()

                ' تحديث جدول المشتركين: حذف المشترك نهائياً إن أردت
                Dim deleteSubCmd As New SqlCommand("DELETE FROM Subscribers_table WHERE Subscriber_id = @subId", conn)
                deleteSubCmd.Parameters.AddWithValue("@subId", subscriberId)
                deleteSubCmd.ExecuteNonQuery()

                ' أو تحديث جدول العائلة إذا كان الرقم خاص بفرد عائلة
                Dim deleteFamilyCmd As New SqlCommand("DELETE FROM Family_table WHERE FNational_id = @subId", conn)
                deleteFamilyCmd.Parameters.AddWithValue("@subId", subscriberId)
                deleteFamilyCmd.ExecuteNonQuery()

                conn.Close()

                MessageBox.Show("✔️ تم الحذف وتحديث الجداول بنجاح.")
                LoadNeedsFromSubscribers()

            Catch ex As Exception
                conn.Close()
                MessageBox.Show("❌ خطأ أثناء الحذف: " & ex.Message)
            End Try
        End If
    End Sub

    Private Sub But_edit_Click(sender As Object, e As EventArgs) Handles But_edit.Click
        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لتعديله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)

            ' التحقق من وجود NeedID للتعديل المحدد
            If selectedRow.Cells("NeedID").Value Is Nothing OrElse selectedRow.Cells("NeedID").Value Is DBNull.Value Then
                MessageBox.Show("❌ لا يمكن تعديل هذا السجل - معرف الاحتياج غير موجود.")
                Return
            End If

            Dim needId As Integer = Convert.ToInt32(selectedRow.Cells("NeedID").Value)
            Dim subscriberId As Integer = Convert.ToInt32(selectedRow.Cells("SubscriberID").Value)

            ' التحقق من صحة البيانات المدخلة
            If String.IsNullOrWhiteSpace(TextBox_sub_name.Text) Or String.IsNullOrWhiteSpace(TextBox_item_name.Text) Then
                MessageBox.Show("يرجى تعبئة جميع الحقول المطلوبة.")
                Return
            End If

            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) OrElse familyCount < 0 Then
                MessageBox.Show("يرجى إدخال عدد صحيح لأفراد العائلة.")
                Return
            End If

            conn.Open()

            ' تحديث الاحتياج المحدد فقط (وليس جميع الاحتياجات)
            Dim updateNeedCmd As New SqlCommand("UPDATE Needs_table SET Need_type = @type, FamilyNumbe = @family WHERE Need_id = @needId", conn)
            updateNeedCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            updateNeedCmd.Parameters.AddWithValue("@family", familyCount)
            updateNeedCmd.Parameters.AddWithValue("@needId", needId)
            updateNeedCmd.ExecuteNonQuery()

            ' تحديث المادة المرتبطة بهذا الاحتياج فقط
            Dim getItemIdCmd As New SqlCommand("SELECT Item_id FROM Needs_table WHERE Need_id = @needId", conn)
            getItemIdCmd.Parameters.AddWithValue("@needId", needId)
            Dim itemIdResult = getItemIdCmd.ExecuteScalar()

            If itemIdResult IsNot Nothing Then
                Dim itemId As Integer = Convert.ToInt32(itemIdResult)
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim updateItemCmd As New SqlCommand("UPDATE Item_table SET Item_category = @category, Item_name = @name, Item_quantity = @qty WHERE Item_id = @itemId", conn)
                updateItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                updateItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                updateItemCmd.Parameters.AddWithValue("@qty", quantity)
                updateItemCmd.Parameters.AddWithValue("@itemId", itemId)
                updateItemCmd.ExecuteNonQuery()
            End If

            conn.Close()

            MessageBox.Show("✔️ تم التعديل بنجاح.")
            ClearFields()
            LoadNeedsFromSubscribers()

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء التعديل: " & ex.Message)
        End Try
    End Sub

End Class