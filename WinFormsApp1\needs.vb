Imports System.Data.SqlClient

Public Class Needs


    Dim conn As New SqlConnection("Data Source=DESKTOP-803R29V;Initial Catalog=Project_DB;Integrated Security=True")

    Private Sub Needs_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadNeedsFromSubscribers()
    End Sub

    Public Sub LoadNeedsFromSubscribers()
        Dim dt As New DataTable()
        Try
            conn.Open()
            ' استعلام محسن لعرض الاحتياجات بشكل صحيح
            Dim query As String = "
SELECT DISTINCT
    s.Subscriber_id AS SubscriberID,
    s.Full_name AS FullName,
    COALESCE(n.FamilyNumbe, 0) AS FamilyCount,
    COALESCE(n.Need_type, 'غير محدد') AS ItemType,
    COALESCE(i.Item_name, 'غير محدد') AS ItemName,
    COALESCE(i.Item_quantity, 0) AS Quantity,
    'مشترك' AS PersonType,
    n.Need_id AS NeedID
FROM Subscribers_table s
LEFT JOIN Needs_table n ON n.Subscriber_id = s.Subscriber_id
LEFT JOIN Item_table i ON i.Item_id = n.Item_id
WHERE s.Subscriber_id IS NOT NULL

UNION ALL

SELECT DISTINCT
    f.Subscriber_id AS SubscriberID,
    f.Name AS FullName,
    COALESCE(n.FamilyNumbe, 0) AS FamilyCount,
    COALESCE(n.Need_type, 'غير محدد') AS ItemType,
    COALESCE(i.Item_name, 'غير محدد') AS ItemName,
    COALESCE(i.Item_quantity, 0) AS Quantity,
    'فرد عائلة' AS PersonType,
    n.Need_id AS NeedID
FROM Family_table f
LEFT JOIN Needs_table n ON n.Subscriber_id = f.Subscriber_id
LEFT JOIN Item_table i ON i.Item_id = n.Item_id
WHERE f.Subscriber_id IS NOT NULL

ORDER BY FullName;"

            Dim adapter As New SqlDataAdapter(query, conn)
            adapter.Fill(dt)
            conn.Close()

            DataGridView_need.DataSource = dt

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ في تحميل البيانات: " & ex.Message)
        End Try
    End Sub

    Private Sub DataGridView_need_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView_need.CellContentClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = DataGridView_need.Rows(e.RowIndex)
            TextBox_sub_name.Text = row.Cells("FullName").Value.ToString()
            family_relate.Text = row.Cells("FamilyCount").Value.ToString()
            TextBox_item_type.Text = row.Cells("ItemType").Value.ToString()
            TextBox_item_name.Text = row.Cells("ItemName").Value.ToString()
            TextBox_quantity.Text = row.Cells("Quantity").Value.ToString()
        End If
    End Sub

    Private Sub Button_save_Click(sender As Object, e As EventArgs) Handles Button_save.Click
        Try
            ' التحقق من الحقول المطلوبة
            If String.IsNullOrEmpty(TextBox_sub_name.Text) Or String.IsNullOrEmpty(TextBox_item_type.Text) Or String.IsNullOrEmpty(TextBox_item_name.Text) Then
                MessageBox.Show("❌ يرجى تعبئة جميع الحقول المطلوبة للحفظ.")
                Exit Sub
            End If

            ' التحقق من صحة عدد أفراد العائلة
            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) Then
                MessageBox.Show("❌ يرجى إدخال رقم صحيح لعدد أفراد العائلة.")
                Exit Sub
            End If

            ' جلب رقم المشترك بناءً على الاسم
            Dim subscriberId As Integer = 0
            Dim getSubIdCmd As New SqlCommand("SELECT Subscriber_id FROM Subscribers_table WHERE Full_name = @name", conn)
            getSubIdCmd.Parameters.AddWithValue("@name", TextBox_sub_name.Text.Trim())

            conn.Open()
            Dim resultSub = getSubIdCmd.ExecuteScalar()
            conn.Close()

            If resultSub Is Nothing Then
                MessageBox.Show("❌ المشترك غير موجود في قاعدة البيانات.")
                Exit Sub
            Else
                subscriberId = Convert.ToInt32(resultSub)
            End If

            ' إنشاء أو جلب المادة
            Dim itemId As Integer = 0

            ' البحث عن المادة الموجودة
            Dim getItemIdCmd As New SqlCommand("SELECT TOP 1 Item_id FROM Item_table WHERE Item_name = @name AND Item_category = @category", conn)
            getItemIdCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
            getItemIdCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())

            conn.Open()
            Dim resultItem = getItemIdCmd.ExecuteScalar()

            If resultItem Is Nothing Then
                ' إنشاء مادة جديدة
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim insertItemCmd As New SqlCommand("INSERT INTO Item_table (Item_name, Item_category, Item_quantity) VALUES (@name, @category, @qty); SELECT SCOPE_IDENTITY();", conn)
                insertItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@qty", quantity)
                itemId = Convert.ToInt32(insertItemCmd.ExecuteScalar())
            Else
                itemId = Convert.ToInt32(resultItem)
            End If

            ' 🔹 التحقق من عدم وجود احتياج مكرر
            Dim checkDuplicateCmd As New SqlCommand("SELECT COUNT(*) FROM Needs_table WHERE Subscriber_id = @subId AND Item_id = @itemId", conn)
            checkDuplicateCmd.Parameters.AddWithValue("@subId", subscriberId)
            checkDuplicateCmd.Parameters.AddWithValue("@itemId", itemId)
            Dim duplicateCount As Integer = Convert.ToInt32(checkDuplicateCmd.ExecuteScalar())

            If duplicateCount > 0 Then
                conn.Close()
                MessageBox.Show("⚠️ هذا الاحتياج موجود بالفعل لهذا الشخص.")
                Exit Sub
            End If

            ' إضافة الاحتياج الجديد
            Dim insertNeedCmd As New SqlCommand("INSERT INTO Needs_table (Subscriber_id, Need_type, FamilyNumbe, Item_id) VALUES (@subId, @type, @family, @itemId)", conn)
            insertNeedCmd.Parameters.AddWithValue("@subId", subscriberId)
            insertNeedCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            insertNeedCmd.Parameters.AddWithValue("@family", familyCount)
            insertNeedCmd.Parameters.AddWithValue("@itemId", itemId)
            insertNeedCmd.ExecuteNonQuery()

            conn.Close()

            MessageBox.Show("✔️ تم حفظ الاحتياج بنجاح.")
            LoadNeedsFromSubscribers()

            ' تصفير الحقول بعد الحفظ الناجح
            TextBox_sub_name.Text = ""
            TextBox_item_type.Text = ""
            TextBox_item_name.Text = ""
            TextBox_quantity.Text = ""
            family_relate.Text = ""

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء الحفظ: " & ex.Message)
        End Try
    End Sub


    Private Sub Butt_delete_Click(sender As Object, e As EventArgs) Handles Butt_delete.Click

        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لحذفه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)

            ' التحقق من وجود NeedID للحذف المحدد
            If selectedRow.Cells("NeedID").Value Is Nothing OrElse selectedRow.Cells("NeedID").Value Is DBNull.Value Then
                MessageBox.Show("❌ لا يمكن حذف هذا السجل - معرف الاحتياج غير موجود.")
                Return
            End If

            Dim needId As Integer = Convert.ToInt32(selectedRow.Cells("NeedID").Value)
            Dim subscriberName As String = selectedRow.Cells("FullName").Value.ToString()
            Dim itemName As String = selectedRow.Cells("ItemName").Value.ToString()

            ' تأكيد الحذف
            Dim result As DialogResult = MessageBox.Show($"هل أنت متأكد من حذف احتياج '{itemName}' للمشترك '{subscriberName}'؟",
                                                       "تأكيد الحذف",
                                                       MessageBoxButtons.YesNo,
                                                       MessageBoxIcon.Question)

            If result = DialogResult.No Then
                Return
            End If

            conn.Open()

            ' حذف الاحتياج المحدد فقط
            Dim deleteNeedCmd As New SqlCommand("DELETE FROM Needs_table WHERE Need_id = @needId", conn)
            deleteNeedCmd.Parameters.AddWithValue("@needId", needId)
            Dim rowsAffected As Integer = deleteNeedCmd.ExecuteNonQuery()

            conn.Close()

            If rowsAffected > 0 Then
                MessageBox.Show("✔️ تم حذف الاحتياج المحدد بنجاح.")
                LoadNeedsFromSubscribers()

                ' تصفير الحقول بعد الحذف
                TextBox_sub_name.Text = ""
                TextBox_item_type.Text = ""
                TextBox_item_name.Text = ""
                TextBox_quantity.Text = ""
                family_relate.Text = ""
            Else
                MessageBox.Show("❌ لم يتم العثور على الاحتياج المحدد للحذف.")
            End If

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء الحذف: " & ex.Message)
        End Try

    End Sub

    Private Sub But_edit_Click(sender As Object, e As EventArgs) Handles But_edit.Click

        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لتعديله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)

            ' التحقق من وجود NeedID للتعديل المحدد
            If selectedRow.Cells("NeedID").Value Is Nothing OrElse selectedRow.Cells("NeedID").Value Is DBNull.Value Then
                MessageBox.Show("❌ لا يمكن تعديل هذا السجل - معرف الاحتياج غير موجود.")
                Return
            End If

            Dim needId As Integer = Convert.ToInt32(selectedRow.Cells("NeedID").Value)
            Dim subscriberId As Integer = Convert.ToInt32(selectedRow.Cells("SubscriberID").Value)

            ' التحقق من صحة البيانات المدخلة
            If String.IsNullOrEmpty(TextBox_sub_name.Text) Or String.IsNullOrEmpty(TextBox_item_type.Text) Then
                MessageBox.Show("❌ يرجى تعبئة جميع الحقول المطلوبة.")
                Return
            End If

            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) Then
                familyCount = 0
            End If

            conn.Open()

            ' تحديث الاحتياج المحدد فقط (وليس جميع الاحتياجات)
            Dim updateNeedCmd As New SqlCommand("UPDATE Needs_table SET Need_type = @type, FamilyNumbe = @family WHERE Need_id = @needId", conn)
            updateNeedCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            updateNeedCmd.Parameters.AddWithValue("@family", familyCount)
            updateNeedCmd.Parameters.AddWithValue("@needId", needId)
            updateNeedCmd.ExecuteNonQuery()

            ' تحديث المادة المرتبطة بهذا الاحتياج فقط
            Dim getItemIdCmd As New SqlCommand("SELECT Item_id FROM Needs_table WHERE Need_id = @needId", conn)
            getItemIdCmd.Parameters.AddWithValue("@needId", needId)
            Dim itemIdResult = getItemIdCmd.ExecuteScalar()

            If itemIdResult IsNot Nothing Then
                Dim itemId As Integer = Convert.ToInt32(itemIdResult)
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim updateItemCmd As New SqlCommand("UPDATE Item_table SET Item_category = @category, Item_name = @name, Item_quantity = @qty WHERE Item_id = @itemId", conn)
                updateItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                updateItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                updateItemCmd.Parameters.AddWithValue("@qty", quantity)
                updateItemCmd.Parameters.AddWithValue("@itemId", itemId)
                updateItemCmd.ExecuteNonQuery()
            End If

            conn.Close()

            MessageBox.Show("✔️ تم تعديل الاحتياج المحدد بنجاح.")
            LoadNeedsFromSubscribers()

            ' تصفير الحقول بعد التعديل الناجح
            TextBox_sub_name.Text = ""
            TextBox_item_type.Text = ""
            TextBox_item_name.Text = ""
            TextBox_quantity.Text = ""
            family_relate.Text = ""

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء التعديل: " & ex.Message)
        End Try

    End Sub

End Class